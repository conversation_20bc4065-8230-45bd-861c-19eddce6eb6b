2025/07/29-19:47:55.249 4d40 Creating DB C:\Users\<USER>\AppData\Local\Temp\flutter_tools.81ae6ecb\flutter_tools_chrome_device.75b6bac3\Default\IndexedDB\http_localhost_24018.indexeddb.leveldb since it was missing.
2025/07/29-19:47:55.262 4d40 Reusing MANIFEST C:\Users\<USER>\AppData\Local\Temp\flutter_tools.81ae6ecb\flutter_tools_chrome_device.75b6bac3\Default\IndexedDB\http_localhost_24018.indexeddb.leveldb/MANIFEST-000001
2025/07/29-19:47:55.283 3ab8 Level-0 table #5: started
2025/07/29-19:47:55.288 3ab8 Level-0 table #5: 1262 bytes OK
2025/07/29-19:47:55.290 3ab8 Delete type=0 #3
2025/07/29-19:47:55.291 4d40 Manual compaction at level-0 from '\x00\x01\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x02\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/07/29-20:55:26.007 12a8 Level-0 table #7: started
2025/07/29-20:55:26.014 12a8 Level-0 table #7: 7072 bytes OK
2025/07/29-20:55:26.020 12a8 Delete type=0 #4
2025/07/29-20:55:26.020 12a8 Manual compaction at level-0 from (begin) .. (end); will stop at (end)
2025/07/29-20:55:26.020 12a8 Manual compaction at level-1 from (begin) .. (end); will stop at '\x00\x03\x01\x02\x013\x00[\x00D\x00E\x00F\x00A\x00U\x00L\x00T\x00]\x00!\x001\x00:\x005\x003\x001\x009\x007\x004\x002\x009\x008\x001\x008\x009\x00:\x00w\x00e\x00b\x00:\x006\x004\x00c\x002\x003\x003\x000\x003\x009\x00f\x001\x003\x00e\x002\x007\x00a\x00b\x008\x00d\x00a\x000\x004' @ 122 : 1
2025/07/29-20:55:26.020 12a8 Compacting 1@1 + 1@2 files
2025/07/29-20:55:26.022 12a8 Generated table #8@1: 36 keys, 2887 bytes
2025/07/29-20:55:26.022 12a8 Compacted 1@1 + 1@2 files => 2887 bytes
2025/07/29-20:55:26.024 12a8 compacted to: files[ 0 0 1 0 0 0 0 ]
2025/07/29-20:55:26.025 12a8 Delete type=2 #5
2025/07/29-20:55:26.025 12a8 Delete type=2 #7
2025/07/29-20:55:26.025 12a8 Manual compaction at level-1 from '\x00\x03\x01\x02\x013\x00[\x00D\x00E\x00F\x00A\x00U\x00L\x00T\x00]\x00!\x001\x00:\x005\x003\x001\x009\x007\x004\x002\x009\x008\x001\x008\x009\x00:\x00w\x00e\x00b\x00:\x006\x004\x00c\x002\x003\x003\x000\x003\x009\x00f\x001\x003\x00e\x002\x007\x00a\x00b\x008\x00d\x00a\x000\x004' @ 122 : 1 .. (end); will stop at (end)
